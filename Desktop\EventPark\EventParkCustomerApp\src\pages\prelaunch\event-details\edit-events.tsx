import { useNavigate } from 'react-router-dom';
import { useState, useRef, useEffect, useCallback } from 'react';
import { RefreshCw, X } from 'lucide-react';
import { ArrowLeft, Calendar, Clock } from 'iconsax-react';
import { formatDate, formattingTime } from '../../../lib/helpers';
import { useEventStore } from '../../../lib/store/event';
import { format } from 'date-fns';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  events,
  UpdateEventPayload,
  EventCategories,
} from '../../../lib/services/events';
import { useEventManagement } from '../../../lib/hooks/useEventManagement';
import { toast } from 'react-toastify';
import {
  EditEventModal,
  SwitchEventPreferenceModal,
  EventSavedSuccessModal,
} from '../../../components/modals';
import { EventDatePicker } from '../../../components/date-picker';
import { EditEventFormData } from '../../../types/editEvent';
import { DateRange } from 'react-day-picker';
import { useCompleteEventData } from '../../../lib/hooks/useCompleteEventData';
import { AuthServices } from '../../../lib/services/auth';
import open from '../../../assets/images/open.png';
import priv from '../../../assets/images/private.png';

export const EditEvents = () => {
  const navigate = useNavigate();
  const { selectedEvent } = useEventStore();
  const { updateEventOptimistically } = useEventManagement();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isSwitchModalOpen, setIsSwitchModalOpen] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [pendingPreference, setPendingPreference] = useState<string>('');
  const [formData, setFormData] = useState<EditEventFormData>({
    eventName: '',
    eventDescription: '',
    eventDate: '',
    eventTime: '',
    preference: 'Private Event',
    location: '',
    locationPlaceId: '',
    categoryId: '',
    categoryName: '',
  });
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    selectedEvent?.date_from
      ? {
          from: new Date(selectedEvent.date_from),
          to: selectedEvent?.date_to
            ? new Date(selectedEvent.date_to)
            : undefined,
        }
      : undefined
  );
  const [showTimePicker, setShowTimePicker] = useState(false);
  const timePickerRef = useRef<HTMLDivElement>(null);
  const [changingImageId, setChangingImageId] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const fileDialogOpenedRef = useRef<boolean>(false);

  const {
    completeEventData,
    isLoading: isLoadingCompleteData,
    error: completeDataError,
    refetch: refetchCompleteEventData,
  } = useCompleteEventData(selectedEvent?.id);

  const { data: categoriesData } = useQuery({
    queryKey: ['eventCategories'],
    queryFn: () => events.getEventCategories(),
  });

  const categories: EventCategories[] = categoriesData?.data || [];
  // console.log('complete events', completeEventData);
  const images = completeEventData?.images || [];

  const [hoveredImage, setHoveredImage] = useState<string | null>(null);
  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2);
    const minute = i % 2 === 0 ? '00' : '30';
    return `${hour.toString().padStart(2, '0')}:${minute}`;
  });
  const updateEventMutation = useMutation({
    mutationFn: (payload: UpdateEventPayload) => {
      if (!selectedEvent?.id) throw new Error('No event selected');
      return events.updateEventDetails(selectedEvent.id, payload);
    },
    onSuccess: (response) => {
      const updatedEvent = response.data;
      updateEventOptimistically(updatedEvent);
      refetchCompleteEventData(true); // Silent refetch
      handleSaveSuccess();
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to update event';
      toast.error(errorMessage);
    },
  });

  const deleteImageMutation = useMutation({
    mutationFn: (imageId: string) => {
      return events.deleteEventImage(imageId);
    },
    onSuccess: () => {
      toast.success('Image deleted successfully!');
      refetchCompleteEventData(true); // Silent refetch
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to delete image';
      toast.error(errorMessage);
    },
  });

  const uploadImageMutation = useMutation({
    mutationFn: ({ file, eventId }: { file: File; eventId: string }) => {
      return AuthServices.uploadFiles(file, 'event_image', eventId);
    },
    onSuccess: () => {
      toast.success('Image changed successfully!');
      setChangingImageId(null);
      refetchCompleteEventData(true); // Silent refetch
    },
    onError: (error: unknown) => {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to upload new image';
      toast.error(errorMessage);
      setChangingImageId(null);
    },
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleDateRangeSelect = (range: DateRange | undefined) => {
    if (range) {
      setDateRange(range);
      if (range.from) {
        const dateStr = format(range.from, 'yyyy-MM-dd');
        handleInputChange('eventDate', dateStr);
      }
    }
  };

  const handleTimeSelect = (time: string) => {
    handleInputChange('eventTime', time);
    setShowTimePicker(false);
  };

  const handlePreferenceChangeRequest = (newPreference: string) => {
    setPendingPreference(newPreference);
    setIsSwitchModalOpen(true);
  };

  const handleConfirmPreferenceSwitch = () => {
    handleInputChange('preference', pendingPreference);
    setIsSwitchModalOpen(false);
    setPendingPreference('');
  };

  const handleCancelPreferenceSwitch = () => {
    setIsSwitchModalOpen(false);
    setPendingPreference('');
  };
  const handleSaveEvent = () => {
    if (!selectedEvent?.id) {
      toast.error('No event selected');
      return;
    }

    const preferenceToVisibility = (preference: string) => {
      switch (preference) {
        case 'Open Event':
          return 'public';
        case 'Private Event':
          return 'private';
        case 'Invite Only':
          return 'invite_only';
        default:
          return 'private';
      }
    };

    const convertTo24HourFormat = (time12h: string) => {
      const time12hLower = time12h.toLowerCase().trim();

      if (!time12hLower.includes('am') && !time12hLower.includes('pm')) {
        return time12h;
      }

      const [time, modifier] = time12hLower.split(/\s*(am|pm)\s*/);
      const timeParts = time.split(':').map(Number);
      let hours = timeParts[0];
      const minutes = timeParts[1];

      if (isNaN(hours) || isNaN(minutes)) {
        throw new Error('Invalid time format');
      }

      if (modifier === 'pm' && hours !== 12) {
        hours += 12;
      } else if (modifier === 'am' && hours === 12) {
        hours = 0;
      }

      return `${hours.toString().padStart(2, '0')}:${minutes
        .toString()
        .padStart(2, '0')}`;
    };

    const formatDateTimeForAPI = (date: string, time: string) => {
      if (!date || !time) {
        throw new Error('Date and time are required');
      }

      const time24h = convertTo24HourFormat(time);

      const timeParts = time24h.split(':');
      if (timeParts.length !== 2) {
        throw new Error('Invalid time format. Expected HH:MM');
      }

      const [hours, minutes] = timeParts.map(Number);

      if (
        isNaN(hours) ||
        isNaN(minutes) ||
        hours < 0 ||
        hours > 23 ||
        minutes < 0 ||
        minutes > 59
      ) {
        throw new Error('Invalid time values');
      }

      const eventDate = new Date(date);

      if (isNaN(eventDate.getTime())) {
        throw new Error('Invalid date format');
      }

      eventDate.setHours(hours, minutes, 0, 0);

      if (isNaN(eventDate.getTime())) {
        throw new Error('Invalid date/time combination');
      }

      return eventDate.toISOString();
    };

    const payload: UpdateEventPayload = {
      title: formData.eventName,
      description: formData.eventDescription,
      visibility: preferenceToVisibility(formData.preference),
    };

    if (formData.eventDate && formData.eventTime) {
      try {
        payload.date_from = formatDateTimeForAPI(
          formData.eventDate,
          formData.eventTime
        );

        if (dateRange?.to) {
          payload.date_to = formatDateTimeForAPI(
            format(dateRange.to, 'yyyy-MM-dd'),
            formData.eventTime
          );
        } else {
          payload.date_to = payload.date_from;
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Invalid date/time format';
        toast.error(`Date/Time Error: ${errorMessage}`);
        return;
      }
    }

    if (formData.categoryId) {
      payload.category_id = formData.categoryId;
    }

    if (formData.locationPlaceId) {
      payload.location_place_id = formData.locationPlaceId;
    }

    updateEventMutation.mutate(payload);
  };

  const handleSaveSuccess = () => {
    setIsEditModalOpen(false);
    setIsSuccessModalOpen(true);
  };

  const handleDeleteImage = (imageId: string) => {
    deleteImageMutation.mutate(imageId);
  };

  const validateImageFile = (file: File): boolean => {
    const MAX_FILE_SIZE_MB = Number(import.meta.env.VITE_MAX_IMAGE_SIZE) || 10;
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return false;
    }

    if (file.size > MAX_FILE_SIZE_BYTES) {
      toast.error(`Image size must be less than ${MAX_FILE_SIZE_MB}MB`);
      return false;
    }

    return true;
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];

    // If no file is selected (user cancelled), reset the changing state
    if (!file) {
      setChangingImageId(null);
      return;
    }

    if (!changingImageId || !selectedEvent?.id) {
      setChangingImageId(null);
      return;
    }

    if (!validateImageFile(file)) {
      setChangingImageId(null);
      return;
    }

    try {
      // First delete the old image
      await deleteImageMutation.mutateAsync(changingImageId);

      // Then upload the new image
      await uploadImageMutation.mutateAsync({
        file,
        eventId: selectedEvent.id,
      });
    } catch (error) {
      console.error('Error changing image:', error);
      // Error handling is done in the mutations
      setChangingImageId(null);
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleFileInputClick = () => {
    fileDialogOpenedRef.current = true;
  };

  const handleWindowFocus = useCallback(() => {
    // When window regains focus after file dialog, check if file was selected
    if (fileDialogOpenedRef.current) {
      setTimeout(() => {
        if (
          fileInputRef.current &&
          !fileInputRef.current.files?.length &&
          changingImageId
        ) {
          setChangingImageId(null);
        }
        fileDialogOpenedRef.current = false;
      }, 100);
    }
  }, [changingImageId]);

  const handleChangeImage = (imageId: string) => {
    setChangingImageId(imageId);
    fileInputRef.current?.click();
  };

  useEffect(() => {
    if (completeEventData) {
      const visibilityToPreference = (visibility: string) => {
        switch (visibility?.toLowerCase()) {
          case 'public':
            return 'Open Event';
          case 'private':
            return 'Private Event';
          case 'invite_only':
            return 'Invite Only';
          default:
            return 'Private Event';
        }
      };

      setFormData({
        eventName: completeEventData.title || '',
        eventDescription: completeEventData.description || '',
        eventDate: completeEventData.date_from
          ? format(new Date(completeEventData.date_from), 'yyyy-MM-dd')
          : '',
        eventTime: completeEventData.date_from
          ? formattingTime(completeEventData.date_from)
          : '',
        preference: visibilityToPreference(completeEventData.visibility),
        location: completeEventData.location_address || '',
        locationPlaceId: '',
        categoryId: completeEventData.category_id || '',
        categoryName: completeEventData.category_name || '',
      });

      if (completeEventData.date_from) {
        const fromDate = new Date(completeEventData.date_from);
        const toDate = completeEventData.date_to
          ? new Date(completeEventData.date_to)
          : undefined;
        setDateRange({
          from: fromDate,
          to: toDate,
        });
      }
    }
  }, [completeEventData]);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        timePickerRef.current &&
        !timePickerRef.current.contains(event.target as Node)
      ) {
        setShowTimePicker(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showDatePicker) {
          setShowDatePicker(false);
        } else if (showTimePicker) {
          setShowTimePicker(false);
        } else if (isEditModalOpen) {
          setIsEditModalOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    window.addEventListener('focus', handleWindowFocus);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [showDatePicker, showTimePicker, isEditModalOpen, changingImageId]);

  const eventDate = formatDate(
    completeEventData?.date_from || selectedEvent?.date_from
  );
  const eventTime = formattingTime(
    completeEventData?.date_from || selectedEvent?.date_from
  );

  const dateRangeText =
    dateRange?.from && dateRange?.to
      ? `${format(dateRange.from, 'MMM dd')} - ${format(
          dateRange.to,
          'MMM dd, yyyy'
        )}`
      : dateRange?.from
      ? format(dateRange.from, 'MMM dd, yyyy')
      : 'Select Date Range';

  if (completeDataError)
    return (
      <div className="flex justify-center items-center min-h-[100vh]">
        Sorry, An Error Occured, Kindly Refresh
      </div>
    );
  if (isLoadingCompleteData)
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full" />
      </div>
    );
  return (
    <div className="bg-[linear-gradient(229.47deg,_#FEFAF8_38.81%,_#F5F6FE_851.11%)]">
      <div className=" min-h-screen max-w-[1160px] mx-auto px-4 sm:px-6 lg:px-0">
        <div className="flex items-center gap-3 py-4 sm:py-6 lg:py-8">
          <button
            type="button"
            onClick={() => navigate(-1)}
            className={` bg-white rounded-full cursor-pointer `}>
            <div className="bg-[#F5F6FE] w-fit p-0.5">
              <div className="bg-primary p-0.5 rounded-full">
                <ArrowLeft size="16" color="#fff" />
              </div>{' '}
            </div>
          </button>
          <h1 className="text-base sm:text-lg font-semibold text-gray-900">
            Event Details
          </h1>
        </div>
        <div className="">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mb-6 sm:mb-8 bg-white py-4 sm:py-6 lg:py-9 px-3 sm:px-6 lg:px-10 rounded-lg sm:rounded-none">
            <div className="w-full sm:w-[130px] h-[200px] sm:h-[130px] rounded-xl overflow-hidden flex-shrink-0">
              <img
                src={
                  completeEventData?.banner_preview_url ||
                  'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=800&h=600&fit=crop'
                }
                alt="Event Banner"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-center  gap-3 sm:gap-4">
                <h2 className="text-xl sm:text-2xl lg:text-[36px] font-medium leading-tight break-words pr-2 sm:pr-0">
                  {completeEventData?.title || ''}
                </h2>
                <div className="flex items-center gap-2">
                  {completeEventData?.visibility === 'public' ? (
                    <>
                      <img src={open} alt="open-events" className="w-8 h-8" />
                      <span className="text-gray-500 font-medium text-sm tracking-wider">
                        OPEN EVENT
                      </span>
                    </>
                  ) : (
                    <>
                      <img
                        src={priv}
                        alt="private-events"
                        className="w-8 h-8"
                      />
                      <span className="text-gray-500 font-medium text-sm tracking-wider">
                        PRIVATE EVENT
                      </span>
                    </>
                  )}
                </div>
              </div>
              <p className="text-grey-250 text-sm sm:text-base mt-2 break-words pr-2 sm:pr-0">
                {completeEventData?.description || ''}{' '}
              </p>
              <div className="flex flex-wrap gap-2 sm:gap-4 my-3 sm:my-4">
                <div className="flex items-center gap-1 bg-cus-pink-500 pl-2.5 pr-2 py-0.5 rounded-2xl">
                  <Calendar color="#FF885E" size={12} variant="Bulk" />{' '}
                  <span className="text-xs italic font-medium text-cus-orange-250">
                    {eventDate}
                  </span>
                </div>
                <div className="flex items-center gap-1 bg-primary-700 pl-2.5 pr-2 rounded-2xl py-0.5">
                  <Clock color="#000073" size={12} variant="Bulk" />{' '}
                  <span className="text-dark-blue text-xs italic font-medium">
                    {eventTime}
                  </span>
                </div>
              </div>
              <div className="mt-2 uppercase text-grey-950 text-xs italic break-words pr-2 sm:pr-0">
                📌 <span>{completeEventData?.location_address || ''}</span>
              </div>
            </div>
            <button
              onClick={() => setIsEditModalOpen(true)}
              className="text-dark-blue-100 text-sm bg-primary-250 px-3.5 py-2 rounded-full font-semibold whitespace-nowrap self-start">
              Edit Details
            </button>
          </div>
          {images.length > 0 && (
            <div className="mb-6 px-3 sm:px-0">
              <h3 className="text-base xs:text-lg font-semibold mb-3 xs:mb-4 text-gray-900">
                Event Images
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 xs:gap-4">
                {images.map((image, index) => (
                  <div
                    key={image.id}
                    className="relative group cursor-pointer rounded-xl overflow-hidden"
                    onMouseEnter={() => setHoveredImage(image.id)}
                    onMouseLeave={() => setHoveredImage(null)}>
                    <img
                      src={image.preview_url}
                      alt={`Event image ${index + 1}`}
                      className="w-full h-[300px] xs:h-[350px] sm:h-[400px] md:h-[500px] object-cover"
                    />
                    {hoveredImage === image.id && (
                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center space-x-2 xs:space-x-3 p-2">
                        <button
                          onClick={() => handleChangeImage(image.id)}
                          className="px-2 xs:px-3 md:px-4 py-1.5 xs:py-2 bg-white text-gray-700 rounded-lg text-xs xs:text-sm font-medium hover:bg-gray-100 flex items-center space-x-1 xs:space-x-2"
                          disabled={
                            changingImageId === image.id ||
                            deleteImageMutation.isPending ||
                            uploadImageMutation.isPending
                          }>
                          <RefreshCw
                            size={12}
                            className={`xs:w-3.5 xs:h-3.5 ${
                              changingImageId === image.id ? 'animate-spin' : ''
                            }`}
                          />
                          <span className="hidden xs:inline">
                            {changingImageId === image.id
                              ? 'Changing...'
                              : 'Change Image'}
                          </span>
                          <span className="xs:hidden">
                            {changingImageId === image.id
                              ? 'Changing...'
                              : 'Change'}
                          </span>
                        </button>
                        <button
                          onClick={() => handleDeleteImage(image.id)}
                          className="px-2 xs:px-3 md:px-4 py-1.5 xs:py-2 bg-red-600 text-white rounded-lg text-xs xs:text-sm font-medium hover:bg-red-700 flex items-center space-x-1 xs:space-x-2"
                          disabled={
                            deleteImageMutation.isPending ||
                            changingImageId === image.id ||
                            uploadImageMutation.isPending
                          }>
                          <X size={12} className="xs:w-3.5 xs:h-3.5" />
                          <span className="hidden xs:inline">
                            {deleteImageMutation.isPending
                              ? 'Deleting...'
                              : 'Delete Image'}
                          </span>
                          <span className="xs:hidden">
                            {deleteImageMutation.isPending
                              ? 'Del...'
                              : 'Delete'}
                          </span>
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {images.length === 0 && !isLoadingCompleteData && (
            <div className="mb-6 bg-white rounded-xl p-4 xs:p-6 sm:p-8 text-center mx-3 sm:mx-0">
              <div className="text-gray-400 mb-3 xs:mb-4">
                <svg
                  className="w-12 h-12 xs:w-14 xs:h-14 sm:w-16 sm:h-16 mx-auto"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="text-base xs:text-lg font-medium text-gray-900 mb-2">
                No images uploaded yet
              </h3>
              <p className="text-sm xs:text-base text-gray-500 mb-4">
                Upload some images to showcase your event!
              </p>
              {/* <button
                onClick={() =>
                  toast.info('Image upload functionality coming soon!')
                }
                className="px-6 py-2 bg-primary text-white rounded-lg font-medium hover:bg-primary/90 transition-colors">
                Add Images
              </button> */}
            </div>
          )}

          {isLoadingCompleteData && (
            <div className="mb-6 bg-white rounded-xl p-4 xs:p-6 sm:p-8 text-center mx-3 sm:mx-0">
              <div className="animate-spin h-6 w-6 xs:h-8 xs:w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-3 xs:mb-4" />
              <p className="text-sm xs:text-base text-gray-500">
                Loading images...
              </p>
            </div>
          )}
        </div>
      </div>

      <EditEventModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        formData={formData}
        onInputChange={handleInputChange}
        isLoading={updateEventMutation.isPending}
        dateRangeText={dateRangeText}
        onDatePickerOpen={() => setShowDatePicker(true)}
        showTimePicker={showTimePicker}
        onTimePickerToggle={() => setShowTimePicker(true)}
        timeOptions={timeOptions}
        onTimeSelect={handleTimeSelect}
        timePickerRef={timePickerRef}
        onPreferenceChangeRequest={handlePreferenceChangeRequest}
        onSave={handleSaveEvent}
        onSaveSuccess={handleSaveSuccess}
        categories={categories}
      />

      <SwitchEventPreferenceModal
        isOpen={isSwitchModalOpen}
        onClose={handleCancelPreferenceSwitch}
        onConfirm={handleConfirmPreferenceSwitch}
        currentPreference={formData.preference}
        targetPreference={pendingPreference}
      />

      <EventSavedSuccessModal
        isOpen={isSuccessModalOpen}
        onClose={() => setIsSuccessModalOpen(false)}
      />

      <EventDatePicker
        isOpen={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        dateRange={dateRange}
        onDateRangeSelect={handleDateRangeSelect}
      />

      {/* Hidden file input for image change */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        onClick={handleFileInputClick}
        className="hidden"
      />
    </div>
  );
};
